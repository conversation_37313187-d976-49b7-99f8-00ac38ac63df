import "dotenv/config";
import { dbInit } from "@/database/drizzle/db";
import { insertPackage, getAllPackages } from "@/database/drizzle/queries/packages";
import { PackageInsert } from "@/database/drizzle/schema/packages";

const defaultPackages: PackageInsert[] = [
  {
    name: "Free",
    description: "Perfect for getting started with video creation",
    price: "0.00",
    currency: "usd",
    billingInterval: "month",
    features: [
      "5 videos per month",
      "720p video quality",
      "Basic templates",
      "Community support",
    ],
    isActive: true,
    maxVideos: 5,
    maxStorageGB: 1,
    priority: 1,
  },
  {
    name: "Starter",
    description: "Great for content creators and small businesses",
    price: "9.99",
    currency: "usd",
    billingInterval: "month",
    features: [
      "25 videos per month",
      "1080p video quality",
      "Premium templates",
      "Email support",
      "Custom branding",
    ],
    isActive: true,
    maxVideos: 25,
    maxStorageGB: 5,
    priority: 2,
  },
  {
    name: "Pro",
    description: "Perfect for growing businesses and agencies",
    price: "29.99",
    currency: "usd",
    billingInterval: "month",
    features: [
      "100 videos per month",
      "4K video quality",
      "All templates",
      "Priority support",
      "Custom branding",
      "Advanced analytics",
      "Team collaboration",
    ],
    isActive: true,
    maxVideos: 100,
    maxStorageGB: 20,
    priority: 3,
  },
  {
    name: "Enterprise",
    description: "Unlimited power for large organizations",
    price: "99.99",
    currency: "usd",
    billingInterval: "month",
    features: [
      "Unlimited videos",
      "4K video quality",
      "All templates",
      "24/7 priority support",
      "Custom branding",
      "Advanced analytics",
      "Team collaboration",
      "API access",
      "Custom integrations",
    ],
    isActive: true,
    maxVideos: null, // unlimited
    maxStorageGB: null, // unlimited
    priority: 4,
  },
  {
    name: "Pro Annual",
    description: "Pro plan with annual billing - save 20%",
    price: "287.88", // $29.99 * 12 * 0.8 = $287.88 (20% discount)
    currency: "usd",
    billingInterval: "year",
    features: [
      "100 videos per month",
      "4K video quality",
      "All templates",
      "Priority support",
      "Custom branding",
      "Advanced analytics",
      "Team collaboration",
      "20% annual discount",
    ],
    isActive: true,
    maxVideos: 100,
    maxStorageGB: 20,
    priority: 5,
  },
  {
    name: "Enterprise Annual",
    description: "Enterprise plan with annual billing - save 20%",
    price: "959.88", // $99.99 * 12 * 0.8 = $959.88 (20% discount)
    currency: "usd",
    billingInterval: "year",
    features: [
      "Unlimited videos",
      "4K video quality",
      "All templates",
      "24/7 priority support",
      "Custom branding",
      "Advanced analytics",
      "Team collaboration",
      "API access",
      "Custom integrations",
      "20% annual discount",
    ],
    isActive: true,
    maxVideos: null, // unlimited
    maxStorageGB: null, // unlimited
    priority: 6,
  },
];

async function seedPackages() {
  console.log("🌱 Seeding packages...");

  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is not set");
    process.exit(1);
  }

  console.log(`📡 Connecting to database: ${process.env.DATABASE_URL.replace(/:[^:@]*@/, ":***@")}`);

  const db = dbInit();

  try {
    // Check if packages already exist
    const existingPackages = await getAllPackages(db);
    if (existingPackages.length > 0) {
      console.log(`⚠️  Found ${existingPackages.length} existing packages. Skipping seed to avoid duplicates.`);
      console.log("💡 To re-seed, delete existing packages first or modify the seed script.");
      return;
    }

    for (const packageData of defaultPackages) {
      console.log(`Creating package: ${packageData.name}`);
      await insertPackage(db, packageData);
    }

    console.log("✅ Packages seeded successfully!");
  } catch (error) {
    console.error("❌ Error seeding packages:", error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes("ENOTFOUND") || errorMessage.includes("getaddrinfo")) {
      console.error("💡 Database connection failed. Make sure:");
      console.error("   1. Database server is running");
      console.error("   2. DATABASE_URL is correct");
      console.error("   3. If using Docker: run 'docker-compose up -d db' first");
      console.error("   4. If using local DB: update DATABASE_URL to point to localhost");
    }

    process.exit(1);
  }
}

// Run the seed function if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedPackages();
}

export { seedPackages, defaultPackages };
