import { dbInit } from "@/database/drizzle/db";
import * as schema from "@/database/drizzle/schema/auth";
import type { Get, UniversalMiddleware } from "@universal-middleware/core";
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { nextCookies } from "better-auth/next-js";

export const auth = betterAuth({
  database: drizzleAdapter(dbInit(), {
    provider: "pg",
    schema,
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  trustedOrigins: ["http://localhost:5002", "https://shorts.bisso.app"],
  plugins: [nextCookies()], // make sure this is the last plugin in the array
});

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Universal {
    interface Context {
      session?: Awaited<ReturnType<typeof auth.api.getSession>>;
    }
  }
}

export const authSessionMiddleware: Get<[], UniversalMiddleware> = () => async (request, context) => {
  try {
    return {
      ...context,
      session: await auth.api.getSession({
        headers: request.headers,
      }),
    };
  } catch (error) {
    console.debug("authSessionMiddleware:", error);
    return {
      ...context,
      session: null,
    };
  }
};
