"use client";

import { createContext, useContext } from "react";
import type { Data } from "./data";
export type { Data } from "./data";

const DataContext = createContext<Partial<Data>>({});
export const useData = () => useContext(DataContext);
export const DataContainer = ({ children, data }: { children: React.ReactNode, data: Data }) => {
  return <DataContext value={data}>{children}</DataContext>
};