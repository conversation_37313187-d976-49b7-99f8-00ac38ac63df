"use server";

import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { VideoItem } from "@/database/drizzle/schema/videos";
import { addJob, QGenerateVideo } from "@/jobs/queues";
import { getVoices } from "@/lib/elevenlabs";
import { errors } from "@/lib/errors";
import { getDataContext } from "@/lib/getDataContext";

export async function onUpdateAndGenerateVideo(id: number, data: Partial<VideoItem>) {
  const context = await getDataContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  data.voiceId ||= "tnSpp4vdxKPjI9w0GnoV";
  const record = (await drizzleQueries.updateVideo(context.db, id, data).returning())[0];

  await addJob(context.db, context.session.user.id, QGenerateVideo, {
    videoId: record.id,
  });

  return record;
}

export async function onGetMoreVoices(...args: Parameters<typeof getVoices>) {
  return await getVoices(...args);
}
