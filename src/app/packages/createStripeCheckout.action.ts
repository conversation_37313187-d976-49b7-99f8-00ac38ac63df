"use server";

import * as packageQueries from "@/database/drizzle/queries/packages";
import { getDataContext } from "@/lib/getDataContext";

import { stripe } from "@/lib/stripe";

interface CreateStripeCheckoutParams {
  packageId: number;
  userId: string;
  userEmail: string;
  userName?: string;
}

export async function onCreateStripeCheckout(params: CreateStripeCheckoutParams) {
  const { db } = await getDataContext();
  const { packageId, userId, userEmail } = params;

  // Get the package details
  const [packageData] = await packageQueries.getPackageById(db, packageId);
  if (!packageData) {
    throw new Error("Package not found");
  }

  if (!packageData.stripePriceId) {
    throw new Error("Package does not have a Stripe price ID");
  }

  // For free packages, we don't need Stripe checkout
  if (parseFloat(packageData.price) === 0) {
    throw new Error("Free packages don't require Stripe checkout");
  }

  try {
    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price: packageData.stripePriceId,
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${process.env.FRONTEND_URL || "http://localhost:5002"}/packages?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.FRONTEND_URL || "http://localhost:5002"}/packages?canceled=true`,
      customer_email: userEmail,
      metadata: {
        userId: userId,
        packageId: packageId.toString(),
      },
      subscription_data: {
        metadata: {
          userId: userId,
          packageId: packageId.toString(),
        },
      },
    });

    return {
      checkoutUrl: session.url,
      sessionId: session.id,
    };
  } catch (error) {
    console.error("Failed to create Stripe checkout session:", error);
    throw new Error("Failed to create checkout session");
  }
}
