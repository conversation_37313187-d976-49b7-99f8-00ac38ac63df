import { usePathname } from "next/navigation";
import { ComponentProps } from "react";

export function Link({ href, children, ...props }: Omit<ComponentProps<"a">, "href"> & { href: string }) {
  const urlPathname = usePathname();
  const isActive = href === "/" ? urlPathname === href : urlPathname.startsWith(href);
  return (
    <a href={href} className={isActive ? "is-active" : undefined} {...props}>
      {children}
    </a>
  );
}
