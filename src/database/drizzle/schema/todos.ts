import { integer, pgTable, varchar } from "drizzle-orm/pg-core";

// Example of defining a schema in Drizzle ORM:
export const todoTable = pgTable("todos", {
  id: integer().primaryKey(),
  text: varchar({ length: 255 }).notNull(),
});

// You can then infer the types for selecting and inserting
export type TodoItem = typeof todoTable.$inferSelect;
export type TodoInsert = typeof todoTable.$inferInsert;
