import { relations } from "drizzle-orm";
import { boolean, decimal, integer, jsonb, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { user } from "./auth";

export const packageTable = pgTable("packages", {
  id: integer().primaryKey(),
  name: text().notNull(),
  description: text(),
  price: decimal({ precision: 10, scale: 2 }).notNull(), // Price in dollars
  currency: text().default("usd").notNull(),
  billingInterval: text({ enum: ["month", "year"] }).notNull(),
  features: jsonb().$type<string[]>().default([]),
  isActive: boolean().default(true).notNull(),
  stripePriceId: text(), // Stripe Price ID
  stripeProductId: text(), // Stripe Product ID
  maxVideos: integer(), // Max videos per billing period (null = unlimited)
  maxStorageGB: integer(), // Max storage in GB (null = unlimited)
  priority: integer().default(0).notNull(), // For ordering packages
  createdAt: timestamp().defaultNow().notNull(),
  updatedAt: timestamp().defaultNow().notNull(),
});

export type PackageItem = typeof packageTable.$inferSelect;
export type PackageInsert = typeof packageTable.$inferInsert;

// Relations will be defined after both tables are created

export const subscriptionTable = pgTable("subscriptions", {
  id: integer().primaryKey(),
  userId: text()
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  packageId: integer()
    .notNull()
    .references(() => packageTable.id),
  stripeSubscriptionId: text().unique(), // Stripe Subscription ID
  stripeCustomerId: text(), // Stripe Customer ID
  status: text({
    enum: ["active", "canceled", "incomplete", "incomplete_expired", "past_due", "trialing", "unpaid"],
  }).notNull(),
  currentPeriodStart: timestamp(),
  currentPeriodEnd: timestamp(),
  cancelAtPeriodEnd: boolean().default(false).notNull(),
  canceledAt: timestamp(),
  trialStart: timestamp(),
  trialEnd: timestamp(),
  metadata: jsonb().$type<Record<string, any>>().default({}),
  createdAt: timestamp().defaultNow().notNull(),
  updatedAt: timestamp().defaultNow().notNull(),
});

export type SubscriptionItem = typeof subscriptionTable.$inferSelect;
export type SubscriptionInsert = typeof subscriptionTable.$inferInsert;

export const subscriptionRelations = relations(subscriptionTable, ({ one }) => ({
  user: one(user, {
    fields: [subscriptionTable.userId],
    references: [user.id],
  }),
  package: one(packageTable, {
    fields: [subscriptionTable.packageId],
    references: [packageTable.id],
  }),
}));

// Define relations after both tables are created
export const packageRelations = relations(packageTable, ({ many }) => ({
  subscriptions: many(subscriptionTable),
}));

// Add relations to user table
export const userRelations = relations(user, ({ many }) => ({
  subscriptions: many(subscriptionTable),
}));
