import { getDataContext } from "@/lib/getDataContext";
import { getUserSubscriptionStatus } from "@/lib/subscription-service";
import { getSessionCookie } from "better-auth/cookies";
import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const ignoredPrefixes = [
    //
    "/api",
    "/_next",
    "/webhooks",
  ];

  if (!ignoredPrefixes.some((prefix) => pathname.startsWith(prefix))) {
    const sessionCookie = getSessionCookie(request);

    if (!sessionCookie) {
      const publicPages = [
        //
        "/login",
        "/",
      ];

      if (!publicPages.includes(pathname)) {
        return NextResponse.redirect(new URL("/login", request.url));
      }
    } else {
      const freePages = [
        //
        "/packages",
        "/account/profile",
      ];

      if (!freePages.includes(pathname)) {
        const { db, session } = await getDataContext();

        if (session?.user) {
          const subscriptionStatus = await getUserSubscriptionStatus(db, session.user.id);
          console.log(subscriptionStatus);

          // If user doesn't have an active subscription, redirect to packages page
          if (!subscriptionStatus.hasActiveSubscription) {
            return NextResponse.redirect(new URL("/packages", request.url));
          }
        }
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  runtime: "nodejs",
};
